import { Component, OnInit } from '@angular/core';
import { MegaMenuItem, MenuItem } from 'primeng/api';
import { MegaMenu } from 'primeng/megamenu';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { AvatarModule } from 'primeng/avatar';
import { Menubar } from 'primeng/menubar';
import { BadgeModule } from 'primeng/badge';
import { InputTextModule } from 'primeng/inputtext';
import { Ripple } from 'primeng/ripple';


@Component({
    selector: 'app-header',
    standalone: true,
    imports: [Menubar, BadgeModule, AvatarModule, InputTextModule, CommonModule],
    template: `
    <p-menubar [model]="items" (onFocus)="onFocus($event)">
        <ng-template #start>
            <img src="assets/images/logo_icon/logo.png" alt="logo" width="150" />
        </ng-template>
        <!-- <ng-template #item let-item let-root="root">
            <a pRipple class="flex items-center p-menubar-item-link">
                <span>{{ item.label }}</span>
                <p-badge *ngIf="item.badge" [ngClass]="{ 'ml-auto': !root, 'ml-2': root }" [value]="item.badge" />
                <span *ngIf="item.shortcut" class="ml-auto border border-surface rounded bg-emphasis text-muted-color text-xs p-1">{{ item.shortcut }}</span>
                <i *ngIf="item.items" [ngClass]="['ml-auto pi', root ? 'pi-angle-down' : 'pi-angle-right']"></i>
            </a>
        </ng-template> -->
        <!-- <ng-template #end>
            <div class="flex items-center gap-2">
                <input type="text" pInputText placeholder="Search" class="w-36" />
                <p-avatar image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png" shape="circle" />
            </div>
        </ng-template> -->
    </p-menubar>
    `,
    styles: [
        `
            // :host ::ng-deep p-megamenu-sub {
            //     margin-left: auto;
            // }

            // @media (max-width: 960px) {
            //     :host ::ng-deep p-megamenu-sub {
            //         display: contents;
            //     }
            // }
            // :host ::ng-deep .p-megamenu-mobile .p-megamenu-button {
            //     margin-left: auto;
            // }
            :host ::ng-deep .p-menubar {
                justify-content: space-between;
            }
            :host ::ng-deep .p-menubar-end {
               display: none;
            }
        `,
    ],
})
export class AppHeader implements OnInit {

    items: MenuItem[] | undefined;
    constructor() { }

    ngOnInit() {
        this.items = [
            {
                label: 'Home',
                icon: 'pi pi-home',
                title: 'Home',
                tooltip: 'Home',
                tooltipPosition: 'bottom',
                routerLink: '/'
            },
            {
                label: 'Help',
                icon: 'pi pi-question',
                title: 'Help',
                tooltip: 'Help',
                tooltipPosition: 'bottom',
                routerLink: '/help'
            },
            {
                label: 'Login',
                icon: 'pi pi-sign-in',
                title: 'Login',
                tooltip: 'Login',
                tooltipPosition: 'bottom',
                command: () => {
                    window.location.href = '/login';
                }
            },
            {
                label: 'Register',
                icon: ' pi pi-user-plus',
                title: 'Register',
                tooltip: 'Register',
                tooltipPosition: 'bottom',
                routerLink: '/register'
            },
            // {
            //     label: 'Projects',
            //     icon: 'pi pi-search',
            //     badge: '3',
            //     items: [
            //         {
            //             label: 'Core',
            //             icon: 'pi pi-bolt',
            //             shortcut: '⌘+S',
            //         },
            //         {
            //             label: 'Blocks',
            //             icon: 'pi pi-server',
            //             shortcut: '⌘+B',
            //         },
            //         {
            //             separator: true,
            //         },
            //         {
            //             label: 'UI Kit',
            //             icon: 'pi pi-pencil',
            //             shortcut: '⌘+U',
            //         },
            //     ],
            // },
        ];
    }

    onFocus(event: any) {
        console.log(event);
    }
}
