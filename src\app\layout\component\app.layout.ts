import { Component, OnInit } from '@angular/core';
import { MegaMenuItem } from 'primeng/api';
import { MegaMenu } from 'primeng/megamenu';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { AvatarModule } from 'primeng/avatar';
import { AppHeader } from './app.header';
import { RouterOutlet } from '@angular/router';
import { AppFooter } from './app.footer';

@Component({
    selector: 'app-layout',
    standalone: true,
    imports: [RouterOutlet, AppHeader,AppFooter, CommonModule, ButtonModule, AvatarModule],
    template: `
            <div class="layout-wrapper">
                <div class="sticky top-0 z-50">
                        <app-header />
                </div>
                <div class="layout-main-container">
                    <div class="layout-main">
                        <router-outlet />
                    </div>
                </div>
                <app-footer />
             </div>
             `,
})
export class AppLayout implements OnInit {

    ngOnInit() {

    }

}
