import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { RippleModule } from 'primeng/ripple';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [CardModule, InputTextModule, PasswordModule, ButtonModule, RippleModule, CommonModule],
    template: `
        <div class="flex justify-center">
            <p-card class="w-full sm:w-3/4 md:w-1/2 lg:w-1/3">
                <ng-template pTemplate="title">
                    <h1 class="text-2xl font-bold">Login</h1>
                </ng-template>
                <ng-template pTemplate="content">
                    <form>
                        <div class="mb-4">
                            <label for="email" class="block mb-2">Email</label>
                            <input type="text" pInputText id="email" class="w-full" />
                        </div>
                        <div class="mb-4">
                            <label for="password" class="block mb-2">Password</label>
                            <p-password id="password" class="w-full"></p-password>
                        </div>
                        <button pButton label="Login" class="w-full"></button>
                    </form>
                </ng-template>
            </p-card>
        </div>
    `,
})
export class LoginComponent {}
    